package dify

import (
	"encoding/json"
	"one-api/dto"
)

type DifyChatRequest struct {
	Inputs           map[string]interface{} `json:"inputs"`
	Query            string                 `json:"query"`
	ResponseMode     string                 `json:"response_mode"`
	User             string                 `json:"user"`
	ConversationId   string                 `json:"conversation_id,omitempty"`
	AutoGenerateName bool                   `json:"auto_generate_name"`
	Files            []DifyFile             `json:"files"`
	// 使用 map 来存储额外的自定义字段
	ExtraFields      map[string]interface{} `json:"-"`
}

// MarshalJSON 自定义 JSON 序列化，将 ExtraFields 合并到主结构中
func (d *DifyChatRequest) MarshalJSON() ([]byte, error) {
	// 创建一个临时的 map 来存储所有字段
	result := make(map[string]interface{})

	// 添加基本字段
	result["inputs"] = d.Inputs
	result["query"] = d.Query
	result["response_mode"] = d.ResponseMode
	result["user"] = d.User
	if d.ConversationId != "" {
		result["conversation_id"] = d.ConversationId
	}
	result["auto_generate_name"] = d.AutoGenerateName
	result["files"] = d.Files

	// 添加额外字段
	for key, value := range d.ExtraFields {
		// 避免覆盖已定义的字段
		if _, exists := result[key]; !exists {
			result[key] = value
		}
	}

	return json.Marshal(result)
}

type DifyFile struct {
	Type         string `json:"type"`
	TransferMode string `json:"transfer_mode"`
	URL          string `json:"url,omitempty"`
	UploadFileId string `json:"upload_file_id,omitempty"`
}

type DifyMetaData struct {
	Usage dto.Usage `json:"usage"`
}

type DifyData struct {
	WorkflowId string `json:"workflow_id"`
	NodeId     string `json:"node_id"`
	NodeType   string `json:"node_type"`
	Status     string `json:"status"`
}

type DifyChatCompletionResponse struct {
	ConversationId string       `json:"conversation_id"`
	Answer         string       `json:"answer"`
	CreateAt       int64        `json:"create_at"`
	MetaData       DifyMetaData `json:"metadata"`
}

type DifyChunkChatCompletionResponse struct {
	Event          string       `json:"event"`
	ConversationId string       `json:"conversation_id"`
	Answer         string       `json:"answer"`
	Data           DifyData     `json:"data"`
	MetaData       DifyMetaData `json:"metadata"`
}
