FROM node:18-alpine AS builder

WORKDIR /build

# 设置 npm 镜像源并安装依赖
COPY web/package.json ./
RUN npm config set registry https://registry.npmmirror.com && \
    npm install --legacy-peer-deps

# 复制源代码和配置文件
COPY ./web .
COPY ./VERSION ./VERSION

# 构建前端
RUN DISABLE_ESLINT_PLUGIN='true' \
    VITE_REACT_APP_VERSION="v1.0.0" \
    NODE_OPTIONS="--max-old-space-size=4096" \
    npm run build

FROM golang:alpine AS builder2

ENV GO111MODULE=on \
    CGO_ENABLED=0 \
    GOOS=linux \
    GOPROXY=https://goproxy.cn,direct

WORKDIR /build

ADD go.mod go.sum ./
RUN go mod download

COPY . .
COPY --from=builder /build/dist ./web/dist
RUN go build -ldflags "-s -w -X 'one-api/common.Version=$(cat VERSION)'" -o one-api

FROM alpine:latest

RUN apk update \
    && apk upgrade \
    && apk add --no-cache ca-certificates tzdata ffmpeg \
    && update-ca-certificates

COPY --from=builder2 /build/one-api /
EXPOSE 3000
WORKDIR /data
ENTRYPOINT ["/one-api"]
